<?php

namespace App\Providers;

use App\Models;
use App\Policies;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        Models\Finance\UserCash::class => Policies\Finance\UserCashPolicy::class,
        \App\Models\Finance\Invoice::class => \App\Policies\Finance\InvoicePolicy::class,
        \App\Models\Finance\InvoicePayment::class => \App\Policies\Finance\InvoicePaymentPolicy::class,
        \App\Models\Finance\JournalEntry::class => \App\Policies\Finance\JournalEntryPolicy::class,
        \Spatie\Permission\Models\Role::class => \App\Policies\RolePolicy::class,
        \App\Models\Finance\CashCategory::class => \App\Policies\CashCategoryPolicy::class,
        \App\Models\Finance\CashAccount::class => \App\Policies\Finance\CashAccountPolicy::class,
        \App\Models\Finance\PurchaseOrder::class => \App\Policies\PurchaseOrderPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        Gate::before(function ($user, $ability) {
            return $user->isSuperAdmin() ? true : null;
        });
        // Gate::after(function ($user, $ability) {
        //     return $user->isSuperAdmin();
        // });
        Gate::define('resource-lock-manager', function ($user) {
            return $user->hasRole(['Admin', 'Super Admin']);
        });
    }
}
