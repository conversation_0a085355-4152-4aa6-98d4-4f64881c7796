<?php

namespace App\Providers\Filament;

use App\Http\Middleware\SetPeriod;
use Awcodes\FilamentQuickCreate\QuickCreatePlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationGroup;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use pxlrbt\FilamentEnvironmentIndicator\EnvironmentIndicatorPlugin;

class FinancePanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('finance')
            ->path('finance')
            // ->spa()
            ->navigationGroups([
                NavigationGroup::make()
                    ->label('Sales')
                    ->icon('heroicon-o-chart-pie')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Purchases')
                    ->icon('heroicon-o-banknotes')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Expenses')
                    ->icon('heroicon-o-stop-circle')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Contacts')
                    ->icon('heroicon-o-identification')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Products & Services')
                    ->icon('phosphor-package')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Settings')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Reports')
                    ->icon('heroicon-o-document-chart-bar')
                    ->collapsed(),
            ])
            ->colors([
                'primary' => Color::hex('#cca864'),
            ])
            ->discoverResources(in: app_path('Filament/Finance/Resources'), for: 'App\\Filament\\Finance\\Resources')
            ->discoverPages(in: app_path('Filament/Finance/Pages'), for: 'App\\Filament\\Finance\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Finance/Widgets'), for: 'App\\Filament\\Finance\\Widgets')
            ->widgets([
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetPeriod::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                EnvironmentIndicatorPlugin::make()
                    ->visible(fn () => auth('web')->user()?->hasRole('Admin')),
                QuickCreatePlugin::make()
                    ->includes([
                        \App\Filament\Finance\Resources\BillResource::class,
                        \App\Filament\Finance\Resources\InvoiceResource::class,
                    ])
                    ->sort(false)
                    ->rounded(false)
                    ->label('New')
                    ->renderUsingHook(PanelsRenderHook::TOPBAR_START),
            ]);
    }
}
