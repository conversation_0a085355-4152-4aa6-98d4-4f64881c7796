<?php

namespace App\Filament\Finance\Resources\JournalEntryResource\Pages;

use App\Filament\Finance\Resources\JournalEntryResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageJournalEntries extends ManageRecords
{
    protected static string $resource = JournalEntryResource::class;

    protected static ?string $title = 'Jurnal Umum';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
