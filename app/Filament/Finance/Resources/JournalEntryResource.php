<?php

namespace App\Filament\Finance\Resources;

use App\Filament\Finance\Resources\JournalEntryResource\Pages;
use App\Models\Finance\CashAccount;
use App\Models\Finance\JournalEntry;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Table;
use Malzariey\FilamentDaterangepickerFilter\Filters\DateRangeFilter;

class JournalEntryResource extends Resource
{
    protected static ?string $model = JournalEntry::class;

    protected static ?string $navigationIcon = 'phosphor-book-open-text';

    protected static ?string $navigationGroup = 'Accounting';

    protected static ?string $navigationLabel = 'Jurnal Umum';

    protected static ?int $navigationSort = 3;

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\DatePicker::make('entry_date')
                    ->default(today())
                    ->required(),
                Forms\Components\TextInput::make('details')
                    ->required(),
                TableRepeater::make('items')
                    ->relationship()
                    ->columnSpanFull()
                    ->minItems(2)
                    ->validationMessages([
                        'min' => 'Harus ada minimal 2 jumlah item (debit & kredit)',
                    ])
                    ->headers([
                        Header::make('Account'),
                        Header::make('Type'),
                        Header::make('Amount'),
                    ])
                    ->schema([
                        Forms\Components\Select::make('account_id')
                            ->relationship('account', 'name')
                            ->required(),
                        Forms\Components\Radio::make('type')
                            ->inline()
                            ->options([
                                'd' => 'Debit',
                                'c' => 'Credit',
                            ])
                            ->default('d')
                            ->required(),
                        Forms\Components\TextInput::make('amount')
                            ->required()
                            ->numeric()
                            ->prefix('SAR'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn ($query) => $query->with(['items', 'transaction']))
            ->columns([
                Tables\Columns\TextColumn::make('entry_date')
                    ->sortable()
                    ->date(),
                Tables\Columns\TextColumn::make('details')
                    ->wrap()
                    ->description(fn ($record) => $record->transaction?->transaction_detail)
                    ->searchable(),
                // Tables\Columns\TextColumn::make('amount')
                //     ->getStateUsing(fn ($record) => $record->items->firstWhere('type', 'd')?->amount ?? 0)
                //     ->currencyRight(),
                Tables\Columns\ViewColumn::make('account')
                    ->view('filament.tables.columns.journal-entry-items-account'),
                Tables\Columns\ViewColumn::make('debit')
                    ->alignEnd()
                    ->view('filament.tables.columns.journal-entry-items-debit'),
                Tables\Columns\ViewColumn::make('credit')
                    ->alignEnd()
                    ->view('filament.tables.columns.journal-entry-items-credit'),
            ])
            ->filters([
                DateRangeFilter::make('entry_date')
                    ->label('Date range')
                    ->withIndicator()
                    ->disableClear()
                    ->ranges(function ($component) {
                        $period = current_period();

                        return [
                            __('filament-daterangepicker-filter::message.today') => [$component->now(), $component->now()],
                            __('filament-daterangepicker-filter::message.yesterday') => [$component->now()->subDay(), $component->now()->subDay()],
                            __('filament-daterangepicker-filter::message.last_7_days') => [$component->now()->subDays(6), $component->now()],
                            __('filament-daterangepicker-filter::message.last_30_days') => [$component->now()->subDays(29), $component->now()],
                            __('filament-daterangepicker-filter::message.this_month') => [$component->now()->startOfMonth(), $component->now()->endOfMonth()],
                            __('filament-daterangepicker-filter::message.last_month') => [$component->now()->subMonth()->startOfMonth(), $component->now()->subMonth()->endOfMonth()],
                            __('filament-daterangepicker-filter::message.this_year') => [$component->now()->startOfYear(), $component->now()->endOfYear()],
                            __('filament-daterangepicker-filter::message.last_year') => [$component->now()->subYear()->startOfYear(), $component->now()->subYear()->endOfYear()],
                            'This Period' => [$period->date_start, $period->date_end],
                        ];
                    })
                    ->indicateUsing(function ($data, $filter) {
                        $datesString = data_get($data, 'entry_date');

                        if (empty($datesString)) {
                            return null;
                        }

                        return "Date range: {$datesString}";
                    })
                    ->columnSpan(2)
                    ->startDate(Carbon::today()->startOfMonth())
                    ->endDate(Carbon::today()->endOfMonth()),
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Account')
                    ->options(fn () => CashAccount::query()->orderBy('code')->pluck('name', 'id'))
                    ->searchable()
                    ->modifyQueryUsing(fn ($data, $query) => $query
                        ->when($data['value'], fn ($query, $account_id) => $query->whereHas('items', fn ($query) => $query->where('account_id', $account_id)))),
            ])
            ->filtersLayout(FiltersLayout::AboveContent)
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => ! $record->transaction_type),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => ! $record->transaction_type),
            ])
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\DeleteBulkAction::make(),
            //     ]),
            // ])
            ->defaultSort('entry_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageJournalEntries::route('/'),
        ];
    }
}
