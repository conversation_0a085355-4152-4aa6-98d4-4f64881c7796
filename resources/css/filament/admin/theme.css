@import '../../../../vendor/filament/filament/resources/css/theme.css';
/* @import '../../../../vendor/saade/filament-fullcalendar/resources/css/filament-fullcalendar.css'; */
@import '../../../../vendor/malzariey/filament-daterangepicker-filter/resources/css/filament-daterangepicker.css';
@import '../../../../vendor/awcodes/filament-table-repeater/resources/css/plugin.css';

@config './tailwind.config.js';

.fi-badge .truncate {
    overflow: unset;
    text-overflow: unset;
    white-space: unset;
}

.fc-dayGridDay-view .fc-daygrid-event {
    white-space: normal !important;
}

.filament-tables-row:nth-child(odd) {
    @apply bg-gray-50;
}

.fi-page-sub-navigation-tabs {
    @apply !flex;
}
.fi-page-sub-navigation-select {
    @apply !hidden;
}

.fi-print-preview .fi-section-content {
    padding: 0;
}

@media only screen {
    .page-break {
        @apply border-t -mx-[1cm] my-[1cm];
    }
}

.fi-sidebar-nav-groups {
    @apply gap-y-3;
}
